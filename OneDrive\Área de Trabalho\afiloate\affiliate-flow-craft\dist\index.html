<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AffiliateFlow Pro - Sistema de 4 Etapas para Afiliação Digital</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Sistema gamificado de 4 etapas para gerar renda com afiliação digital. Fluxo otimizado para máxima conversão.">
    <meta name="keywords" content="afiliação digital, sistema 4 etapas, marketing de afiliados, renda online">
    <meta name="author" content="AffiliateFlow Pro">
    <meta name="robots" content="index, follow">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- EmailJS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'lp-dark': '#0a0a0a',
                        'lp-gray': '#1a1a1a',
                        'lp-light': '#ffffff',
                        'lp-blue': '#3b82f6',
                        'lp-purple': '#8b5cf6',
                        'lp-orange': '#f59e0b',
                        'lp-green': '#10b981',
                    },
                    fontSize: {
                        'hero': ['3.5rem', { lineHeight: '1.1', fontWeight: '800' }],
                        'subtitle': ['1.25rem', { lineHeight: '1.6', fontWeight: '400' }],
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.8s ease-out forwards',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-glow': 'pulseGlow 2s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        },
                        pulseGlow: {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)' },
                            '50%': { boxShadow: '0 0 40px rgba(59, 130, 246, 0.8)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        .glass-dark {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .gradient-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        }
        
        .gradient-secondary {
            background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
        }
        
        .gradient-text-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .gradient-text-secondary {
            background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .shadow-glow {
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
        }
        
        .shadow-glow-orange {
            box-shadow: 0 0 30px rgba(245, 158, 11, 0.3);
        }
        
        .animate-pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
        }
        
        .btn-primary:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(245, 158, 11, 0.4);
        }
        
        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ffffff;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .step-card {
            transition: all 0.5s ease;
        }
        
        .step-card.completed {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%);
            border-color: rgba(16, 185, 129, 0.3);
        }
        
        .step-card.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.1) 100%);
            border-color: rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body class="font-inter bg-lp-dark text-lp-light min-h-screen">
    <!-- Loading Screen -->
    <div id="loadingScreen" class="fixed inset-0 bg-lp-dark z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-lp-light/70">Carregando sistema...</p>
        </div>
    </div>

    <!-- Main Container -->
    <div id="mainContent" class="hidden">
        <!-- Header -->
        <header class="glass-dark border-b border-white/10 sticky top-0 z-40">
            <div class="container mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center">
                            <span class="text-white font-bold text-lg">A</span>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-lp-light">AffiliateFlow Pro</h1>
                            <p class="text-sm text-lp-light/60">Sistema de 4 Etapas</p>
                        </div>
                    </div>
                    
                    <div class="hidden md:flex items-center gap-6">
                        <div class="flex items-center gap-2 text-lp-light/70 text-sm">
                            <div class="w-2 h-2 bg-lp-green rounded-full animate-pulse"></div>
                            <span>Sistema Ativo</span>
                        </div>
                        <div class="flex items-center gap-2 text-lp-light/70 text-sm">
                            <div class="w-2 h-2 bg-lp-orange rounded-full animate-pulse"></div>
                            <span>Suporte 24/7</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Bar Container -->
        <div id="progressContainer" class="hidden py-12">
            <div class="container mx-auto px-4">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-lp-light mb-2">
                        Seu Progresso no Sistema
                    </h2>
                    <p class="text-lp-light/70">
                        Complete todas as etapas para ativar seu link de afiliado
                    </p>
                </div>
                
                <!-- Progress Bar -->
                <div class="relative mb-12">
                    <div class="w-full h-3 bg-lp-gray/30 rounded-full overflow-hidden">
                        <div id="progressBar" class="h-full gradient-secondary rounded-full transition-all duration-1000 ease-out shadow-glow-orange" style="width: 0%"></div>
                    </div>
                    
                    <!-- Progress Indicator -->
                    <div id="progressIndicator" class="absolute top-1/2 transform -translate-y-1/2 w-6 h-6 gradient-secondary rounded-full shadow-glow-orange transition-all duration-1000 ease-out" style="left: 0%">
                        <div class="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    </div>
                </div>

                <!-- Steps Grid -->
                <div id="stepsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Steps will be dynamically generated here -->
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <main class="container mx-auto px-4">
            <!-- Hero Section (Step 0) -->
            <section id="heroSection" class="relative min-h-screen flex items-center justify-center py-20">
                <!-- Background Elements -->
                <div class="absolute inset-0 overflow-hidden">
                    <div class="absolute top-20 left-10 w-72 h-72 gradient-primary rounded-full opacity-10 blur-3xl animate-float"></div>
                    <div class="absolute bottom-20 right-10 w-96 h-96 gradient-secondary rounded-full opacity-10 blur-3xl animate-float" style="animation-delay: 1s;"></div>
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] gradient-primary rounded-full opacity-5 blur-3xl"></div>
                </div>

                <div class="relative z-10 text-center max-w-6xl mx-auto">
                    <!-- Badge -->
                    <div class="inline-flex items-center gap-2 glass-dark rounded-full px-6 py-3 mb-8 animate-fade-in-up">
                        <div class="w-2 h-2 bg-lp-green rounded-full animate-pulse"></div>
                        <span class="text-sm font-medium text-lp-light/80">Sistema Ativo • 2.847 Usuários Online</span>
                    </div>

                    <!-- Main Headline -->
                    <h1 class="text-hero text-lp-light mb-8 animate-fade-in-up" style="animation-delay: 0.2s;">
                        <span class="gradient-text-secondary">Renda Recorrente</span>
                        Indicando IA para Empresas
                        <span class="gradient-text-primary">R$ 15.000/mês até R$ 55.000</span>
                        (Plano Enterprise)
                    </h1>

                    <!-- Subtitle -->
                    <p class="text-subtitle text-lp-light/80 mb-12 max-w-4xl mx-auto animate-fade-in-up" style="animation-delay: 0.4s;">
                        🤖 <strong>GRIP - PLATAFORMA DE IA PARA EMPRESAS:</strong> Indique soluções de IA para empresas e construa renda recorrente.
                        <span class="gradient-text-secondary font-bold">Base de R$ 15.000/mês</span>
                        com <span class="gradient-text-primary font-bold">até R$ 55.000 quando empresa contrata plano Enterprise</span>
                    </p>

                    <!-- Urgency Alert -->
                    <div class="animate-fade-in-up mb-6" style="animation-delay: 0.7s;">
                        <div class="glass-dark rounded-xl p-4 border border-lp-orange/30 max-w-2xl mx-auto">
                            <div class="flex items-center justify-center gap-3 text-lp-orange">
                                <span class="animate-pulse">⚡</span>
                                <span class="font-bold">ATENÇÃO:</span>
                                <span>Apenas 47 vagas restantes para influencers este mês!</span>
                                <span class="animate-pulse">⚡</span>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <div class="animate-fade-in-up" style="animation-delay: 0.8s;">
                        <button id="startButton" class="btn-primary text-white font-bold text-xl px-12 py-6 rounded-2xl shadow-2xl hover-lift">
                            <span id="startButtonText">📚 BAIXAR GUIA GRATUITO</span>
                            <div id="startButtonLoader" class="hidden flex items-center gap-3">
                                <div class="loading-spinner"></div>
                                <span>Processando acesso...</span>
                            </div>
                        </button>
                        <p class="text-sm text-lp-light/60 mt-4">
                            ✅ Renda Recorrente R$ 15K+ • ✅ Potencial R$ 55K Enterprise • ✅ IA para Empresas • ✅ Suporte Profissional
                        </p>
                    </div>

                    <!-- Social Proof Section -->
                    <div class="animate-fade-in-up mt-16" style="animation-delay: 1.0s;">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                            <!-- Stat 1 -->
                            <div class="glass-dark rounded-2xl p-6 text-center hover-lift">
                                <div class="text-3xl font-bold gradient-text-secondary mb-2">R$ 15.000+</div>
                                <div class="text-lp-light/80">Renda recorrente mensal base</div>
                            </div>

                            <!-- Stat 2 -->
                            <div class="glass-dark rounded-2xl p-6 text-center hover-lift">
                                <div class="text-3xl font-bold gradient-text-primary mb-2">R$ 55.000</div>
                                <div class="text-lp-light/80">Quando cliente paga plano Enterprise</div>
                            </div>

                            <!-- Stat 3 -->
                            <div class="glass-dark rounded-2xl p-6 text-center hover-lift">
                                <div class="text-3xl font-bold gradient-text-secondary mb-2">🤖 IA</div>
                                <div class="text-lp-light/80">Soluções de IA para empresas</div>
                            </div>
                        </div>

                        <!-- Testimonials Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto mt-8">
                            <!-- Tech Testimonial -->
                            <div class="glass-dark rounded-2xl p-6 border border-lp-blue/20">
                                <div class="flex items-center gap-3 mb-3">
                                    <div class="w-10 h-10 bg-lp-blue rounded-full flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">R</span>
                                    </div>
                                    <div>
                                        <div class="font-bold text-lp-light text-sm">@RafaelTech</div>
                                        <div class="text-xs text-lp-light/60">89K • Tech/IA</div>
                                    </div>
                                </div>
                                <p class="text-lp-light/90 text-sm italic">
                                    "Minha audiência tech adorou a GRIP! Faturei R$ 31.500 em 2 meses falando sobre IA financeira."
                                </p>
                                <div class="text-xs text-lp-blue mt-2">✅ R$ 31.500 em 60 dias</div>
                            </div>

                            <!-- Business Testimonial -->
                            <div class="glass-dark rounded-2xl p-6 border border-lp-orange/20">
                                <div class="flex items-center gap-3 mb-3">
                                    <div class="w-10 h-10 bg-lp-orange rounded-full flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">C</span>
                                    </div>
                                    <div>
                                        <div class="font-bold text-lp-light text-sm">@CarlaEmpreende</div>
                                        <div class="text-xs text-lp-light/60">156K • Business</div>
                                    </div>
                                </div>
                                <p class="text-lp-light/90 text-sm italic">
                                    "Empreendedores precisam diversificar! Com a GRIP, faturei R$ 55.800 ensinando sobre ROI."
                                </p>
                                <div class="text-xs text-lp-orange mt-2">✅ R$ 55.800 em 90 dias</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step Content Area -->
            <div id="stepContent" class="hidden py-12">
                <!-- Content will be dynamically loaded here -->
            </div>
        </main>
    </div>

    <!-- Strategic Pop-ups -->
    <div id="strategicPopup" class="hidden fixed inset-0 bg-black/80 flex items-center justify-center z-50">
        <div class="glass-dark rounded-3xl p-8 max-w-md mx-4 text-center animate-fade-in-up">
            <div id="popupIcon" class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <span id="popupIconText" class="text-3xl">🚀</span>
            </div>
            <h3 id="popupTitle" class="text-2xl font-bold text-lp-light mb-4">Título do Pop-up</h3>
            <p id="popupMessage" class="text-lp-light/80 mb-6">Mensagem do pop-up</p>
            <div class="space-y-3">
                <button id="popupConfirm" class="btn-primary text-white font-bold px-6 py-3 rounded-xl hover-lift w-full">
                    Confirmar
                </button>
                <button id="popupCancel" class="btn-secondary text-white font-bold px-6 py-3 rounded-xl hover-lift w-full">
                    Cancelar
                </button>
            </div>
        </div>
    </div>

    <script>
        // Função global para forçar mostrar conteúdo
        function forceShowContent() {
            console.log('🔧 Forçando exibição do conteúdo...');
            const loadingScreen = document.getElementById('loadingScreen');
            const mainContent = document.getElementById('mainContent');

            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }
            if (mainContent) {
                mainContent.style.display = 'block';
                mainContent.classList.remove('hidden');
            }

            console.log('✅ Conteúdo forçado a aparecer!');
        }

        // Application State
        class AffiliateFlowApp {
            constructor() {
                this.currentStep = 0;
                this.completedSteps = [];
                this.affiliateLink = '';
                this.isVerified = false;
                this.steps = [
                    { id: 0, title: 'Lead Magnet', description: 'Receba guia gratuito', active: true },
                    { id: 1, title: 'Cadastro GRIP', description: 'Registre-se na plataforma', active: false },
                    { id: 2, title: 'Download App', description: 'Baixe o aplicativo oficial', active: false },
                    { id: 3, title: 'Verificação', description: 'Confirme seu cadastro', active: false },
                    { id: 4, title: 'Kit Influencer', description: 'Receba kit exclusivo', active: false }
                ];

                // Initialize EmailJS
                this.initEmailJS();
                this.init();
            }

            initEmailJS() {
                // Initialize EmailJS with public key (using a demo service)
                emailjs.init("user_demo123456789"); // Demo key for testing
            }

            init() {
                this.setupEventListeners();
                this.hideLoadingScreen();
            }

            setupEventListeners() {
                document.getElementById('startButton').addEventListener('click', () => this.handleStartClick());

                // Pop-up de saída estratégico
                window.addEventListener('beforeunload', (e) => {
                    if (this.currentStep > 0 && this.currentStep < 4) {
                        e.preventDefault();
                        e.returnValue = '⚠️ ATENÇÃO: Você perderá acesso ao kit exclusivo para influencers! Tem certeza que deseja sair?';
                        return e.returnValue;
                    }
                });
            }

            hideLoadingScreen() {
                setTimeout(() => {
                    document.getElementById('loadingScreen').classList.add('hidden');
                    document.getElementById('mainContent').classList.remove('hidden');
                }, 1500);
            }

            showStrategicPopup(config) {
                const popup = document.getElementById('strategicPopup');
                const icon = document.getElementById('popupIconText');
                const title = document.getElementById('popupTitle');
                const message = document.getElementById('popupMessage');
                const confirmBtn = document.getElementById('popupConfirm');
                const cancelBtn = document.getElementById('popupCancel');

                icon.textContent = config.icon;
                title.textContent = config.title;
                message.textContent = config.message;
                confirmBtn.textContent = config.confirmText;
                cancelBtn.textContent = config.cancelText;

                popup.classList.remove('hidden');

                return new Promise((resolve) => {
                    confirmBtn.onclick = () => {
                        popup.classList.add('hidden');
                        resolve(true);
                    };
                    cancelBtn.onclick = () => {
                        popup.classList.add('hidden');
                        resolve(false);
                    };
                });
            }

            async handleStartClick() {
                // Ir direto para a etapa do Lead Magnet
                this.hideHeroSection();
                this.showProgressBar();
                this.goToStep(0);
            }

            generateAffiliateLink() {
                const userId = Math.random().toString(36).substr(2, 9);
                return `https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368&ref=${userId}`;
            }

            completeStep(stepId) {
                if (!this.completedSteps.includes(stepId)) {
                    this.completedSteps.push(stepId);
                    console.log(`✅ Etapa ${stepId} concluída!`);
                }
                this.updateProgress();
            }

            completeStepAndAdvance(currentStep, nextStep) {
                console.log(`🔄 Completando etapa ${currentStep} e avançando para ${nextStep}`);
                this.completeStep(currentStep);
                setTimeout(() => {
                    this.goToStep(nextStep);
                }, 500); // Pequeno delay para mostrar a conclusão
            }

            goToStep(stepId) {
                console.log(`🔄 Avançando para etapa ${stepId}`);
                this.currentStep = stepId;
                this.steps.forEach(step => step.active = step.id === stepId);

                if (stepId > 0) {
                    this.showProgressBar();
                    this.hideHeroSection();
                    this.showStepContent(stepId);
                } else {
                    // Se voltar para step 0, mostrar hero novamente
                    document.getElementById('heroSection').classList.remove('hidden');
                    document.getElementById('progressContainer').classList.add('hidden');
                    document.getElementById('stepContent').classList.add('hidden');
                }

                this.updateProgress();
                console.log(`✅ Etapa ${stepId} ativada. Etapas concluídas:`, this.completedSteps);
            }

            showProgressBar() {
                document.getElementById('progressContainer').classList.remove('hidden');
                this.renderStepsGrid();
            }

            hideHeroSection() {
                document.getElementById('heroSection').classList.add('hidden');
            }

            showStepContent(stepId) {
                const stepContent = document.getElementById('stepContent');
                stepContent.classList.remove('hidden');
                stepContent.innerHTML = this.getStepContent(stepId);
            }

            updateProgress() {
                const progressPercentage = (this.completedSteps.length / (this.steps.length - 1)) * 100;

                const progressBar = document.getElementById('progressBar');
                const progressIndicator = document.getElementById('progressIndicator');

                if (progressBar) {
                    progressBar.style.width = `${progressPercentage}%`;
                }

                if (progressIndicator) {
                    progressIndicator.style.left = `calc(${progressPercentage}% - 12px)`;
                }

                this.renderStepsGrid();
            }

            renderStepsGrid() {
                const stepsGrid = document.getElementById('stepsGrid');
                if (!stepsGrid) return;

                const stepsToShow = this.steps.slice(1); // Skip step 0 (hero)

                stepsGrid.innerHTML = stepsToShow.map((step, index) => {
                    const isCompleted = this.completedSteps.includes(step.id);
                    const isActive = step.active;
                    const stepNumber = index + 1;

                    let cardClass = 'step-card glass-dark rounded-2xl p-6 hover-lift';
                    if (isCompleted) {
                        cardClass += ' completed shadow-glow';
                    } else if (isActive) {
                        cardClass += ' active animate-pulse-glow';
                    }

                    return `
                        <div class="${cardClass}">
                            <div class="flex items-center gap-4 mb-4">
                                <div class="w-12 h-12 rounded-xl flex items-center justify-center ${
                                    isCompleted ? 'bg-lp-green' : isActive ? 'gradient-primary' : 'bg-lp-gray/50'
                                }">
                                    ${isCompleted ?
                                        '<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>' :
                                        `<span class="text-white font-bold">${stepNumber}</span>`
                                    }
                                </div>
                                <div>
                                    <h3 class="font-bold text-lp-light">${step.title}</h3>
                                    <p class="text-sm text-lp-light/60">${step.description}</p>
                                </div>
                            </div>

                            <div class="text-sm text-lp-light/70">
                                ${isCompleted ?
                                    '<span class="text-lp-green font-medium">✅ Concluído</span>' :
                                    isActive ?
                                        '<span class="text-lp-blue font-medium">🔄 Em andamento</span>' :
                                        '<span class="text-lp-light/50">⏳ Aguardando</span>'
                                }
                            </div>
                        </div>
                    `;
                }).join('');
            }

            getStepContent(stepId) {
                switch(stepId) {
                    case 0:
                        return this.getLeadMagnetStepContent();
                    case 1:
                        return this.getWelcomeStepContent();
                    case 2:
                        return this.getAppDownloadStepContent();
                    case 3:
                        return this.getVerificationStepContent();
                    case 4:
                        return this.getCompletionStepContent();
                    default:
                        return '<div>Conteúdo não encontrado</div>';
                }
            }

            getLeadMagnetStepContent() {
                return `
                    <div class="max-w-4xl mx-auto text-center">
                        <div class="glass-dark rounded-3xl p-12 mb-8">
                            <div class="w-20 h-20 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>

                            <h2 class="text-4xl font-bold text-lp-light mb-6">
                                📚 <span class="gradient-text-secondary">Guia Exclusivo</span> para Influencers!
                            </h2>

                            <p class="text-xl text-lp-light/80 mb-8 max-w-3xl mx-auto">
                                <span class="gradient-text-primary font-bold">"DOMINE A NOVA ECONOMIA DIGITAL"</span><br>
                                Guia estratégico para influencers digitais que querem construir renda recorrente com IA empresarial.
                                <strong>Receba GRÁTIS por email e descubra como gerar R$ 15.000+/mês!</strong>
                            </p>

                            <div class="glass-dark rounded-2xl p-6 mb-8 max-w-2xl mx-auto">
                                <h3 class="text-lg font-bold text-lp-light mb-4">📋 O que você vai aprender:</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                                    <div class="flex items-start gap-3">
                                        <span class="text-lp-green text-lg">✅</span>
                                        <span class="text-lp-light/90 text-sm">Como identificar oportunidades em IA empresarial</span>
                                    </div>
                                    <div class="flex items-start gap-3">
                                        <span class="text-lp-green text-lg">✅</span>
                                        <span class="text-lp-light/90 text-sm">Estratégias de renda recorrente para influencers</span>
                                    </div>
                                    <div class="flex items-start gap-3">
                                        <span class="text-lp-green text-lg">✅</span>
                                        <span class="text-lp-light/90 text-sm">Como posicionar-se no mercado de IA</span>
                                    </div>
                                    <div class="flex items-start gap-3">
                                        <span class="text-lp-green text-lg">✅</span>
                                        <span class="text-lp-light/90 text-sm">Técnicas de conversão para tech influencers</span>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-8">
                                <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                                    <input
                                        type="email"
                                        id="leadMagnetEmail"
                                        placeholder="Seu melhor email"
                                        class="flex-1 px-4 py-3 rounded-xl bg-lp-gray border border-lp-light/20 text-lp-light placeholder-lp-light/50 focus:outline-none focus:border-lp-blue"
                                        required
                                    >
                                    <button
                                        onclick="app.handleLeadMagnetDownload()"
                                        class="btn-primary text-white font-bold px-6 py-3 rounded-xl hover-lift whitespace-nowrap"
                                    >
                                        📚 BAIXAR GUIA GRÁTIS
                                    </button>
                                </div>
                                <p class="text-xs text-lp-light/60 mt-3">
                                    ✅ 100% Gratuito • ✅ Sem Spam • ✅ Acesso Imediato
                                </p>
                            </div>

                            <div id="leadMagnetFeedback" class="hidden mb-6">
                                <div class="glass-dark rounded-xl p-4">
                                    <div class="flex items-center gap-3 mb-4">
                                        <div class="loading-spinner"></div>
                                        <span id="leadMagnetMessage">Enviando seu guia...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            getWelcomeStepContent() {
                return `
                    <div class="max-w-4xl mx-auto text-center">
                        <div class="glass-dark rounded-3xl p-12 mb-8">
                            <div class="w-20 h-20 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>

                            <h2 class="text-4xl font-bold text-lp-light mb-6">
                                ✅ <span class="gradient-text-secondary">Cadastro Realizado</span> com Sucesso!
                            </h2>

                            <p class="text-xl text-lp-light/80 mb-8 max-w-2xl mx-auto">
                                Parabéns! Você agora faz parte da rede de afiliados
                                <span class="gradient-text-primary font-bold">GRIP</span>.
                                Vamos configurar tudo para você começar a ganhar!
                            </p>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                                <div class="glass-dark rounded-xl p-6">
                                    <div class="text-3xl mb-3">🎯</div>
                                    <h3 class="font-bold text-lp-light mb-2">Próximo Passo</h3>
                                    <p class="text-sm text-lp-light/70">Baixar o app oficial para acompanhar seus ganhos</p>
                                </div>

                                <div class="glass-dark rounded-xl p-6">
                                    <div class="text-3xl mb-3">📊</div>
                                    <h3 class="font-bold text-lp-light mb-2">Acompanhamento</h3>
                                    <p class="text-sm text-lp-light/70">Monitore suas comissões em tempo real</p>
                                </div>

                                <div class="glass-dark rounded-xl p-6">
                                    <div class="text-3xl mb-3">🎁</div>
                                    <h3 class="font-bold text-lp-light mb-2">Material Exclusivo</h3>
                                    <p class="text-sm text-lp-light/70">Receba estratégias que funcionam</p>
                                </div>
                            </div>

                            <button onclick="app.completeStepAndAdvance(1, 2)" class="btn-primary text-white font-bold text-lg px-8 py-4 rounded-xl hover-lift">
                                Continuar para Download do App →
                            </button>
                        </div>
                    </div>
                `;
            }

            getAppDownloadStepContent() {
                return `
                    <div class="max-w-4xl mx-auto text-center">
                        <div class="glass-dark rounded-3xl p-12 mb-8">
                            <div class="w-20 h-20 gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                            </div>

                            <h2 class="text-4xl font-bold text-lp-light mb-6">
                                📱 Baixe o <span class="gradient-text-secondary">App Oficial</span>
                            </h2>

                            <p class="text-xl text-lp-light/80 mb-8 max-w-2xl mx-auto">
                                Para acessar todas as funcionalidades e receber notificações em tempo real,
                                baixe nosso aplicativo oficial.
                            </p>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 max-w-2xl mx-auto">
                                <button onclick="app.handleAppDownload('android')" class="glass-dark rounded-xl p-6 hover-lift transition-all">
                                    <div class="text-4xl mb-3">🤖</div>
                                    <h3 class="font-bold text-lp-light mb-2">Android</h3>
                                    <p class="text-sm text-lp-light/70">Baixar da Google Play</p>
                                </button>

                                <button onclick="app.handleAppDownload('ios')" class="glass-dark rounded-xl p-6 hover-lift transition-all">
                                    <div class="text-4xl mb-3">🍎</div>
                                    <h3 class="font-bold text-lp-light mb-2">iOS</h3>
                                    <p class="text-sm text-lp-light/70">Baixar da App Store</p>
                                </button>
                            </div>

                            <div id="downloadFeedback" class="hidden mb-6">
                                <div class="glass-dark rounded-xl p-4">
                                    <div class="flex items-center gap-3 mb-4">
                                        <div class="loading-spinner"></div>
                                        <span id="downloadMessage">Redirecionando para a loja...</span>
                                    </div>
                                    <div id="confirmDownloadSection" class="hidden">
                                        <button onclick="app.confirmAppInstalled()" class="btn-primary text-white font-bold px-6 py-3 rounded-xl hover-lift">
                                            ✅ App Instalado - Continuar
                                        </button>
                                        <p class="text-xs text-lp-light/50 mt-2">
                                            Clique apenas após instalar o aplicativo
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            getVerificationStepContent() {
                return `
                    <div class="max-w-4xl mx-auto text-center">
                        <div class="glass-dark rounded-3xl p-12 mb-8">
                            <div class="w-20 h-20 gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>

                            <h2 class="text-4xl font-bold text-lp-light mb-6">
                                🔐 <span class="gradient-text-primary">Verificação</span> de Acesso
                            </h2>

                            <p class="text-xl text-lp-light/80 mb-8 max-w-2xl mx-auto">
                                Agora vamos verificar se você tem acesso ao sistema.
                                Este processo é automático e leva apenas alguns segundos.
                            </p>

                            <div class="mb-8">
                                <div class="glass-dark rounded-xl p-6 max-w-md mx-auto">
                                    <div class="text-3xl mb-3">⚡</div>
                                    <h3 class="font-bold text-lp-light mb-2">Verificação Automática</h3>
                                    <p class="text-sm text-lp-light/70">Validando suas credenciais no sistema</p>
                                </div>
                            </div>

                            <button onclick="app.handleVerification()" class="btn-primary text-white font-bold text-lg px-8 py-4 rounded-xl hover-lift">
                                🔄 Iniciar Verificação
                            </button>

                            <div id="verificationFeedback" class="hidden mt-6">
                                <div class="glass-dark rounded-xl p-4">
                                    <div class="flex items-center gap-3">
                                        <div class="loading-spinner"></div>
                                        <span id="verificationMessage">Verificando seu acesso...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            getCompletionStepContent() {
                return `
                    <div class="max-w-4xl mx-auto text-center">
                        <div class="glass-dark rounded-3xl p-12 mb-8">
                            <div class="w-20 h-20 bg-lp-green rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>

                            <h2 class="text-4xl font-bold text-lp-light mb-6">
                                🤖 <span class="gradient-text-secondary">Materiais IA Empresarial</span> Exclusivos!
                            </h2>

                            <p class="text-xl text-lp-light/80 mb-8 max-w-3xl mx-auto">
                                <span class="gradient-text-primary font-bold">GRIP - SOLUÇÕES DE IA PARA EMPRESAS</span><br>
                                Materiais estratégicos para indicar IA empresarial e construir <span class="gradient-text-secondary font-bold">renda recorrente superior a R$ 15.000/mês</span>,
                                com potencial de crescimento <span class="gradient-text-primary font-bold">até R$ 55.000 quando empresa contrata plano Enterprise</span>.
                                <strong>Quanto mais empresas você indicar, maior sua renda mensal!</strong>
                            </p>

                            <!-- Niche-Specific Materials -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8 max-w-6xl mx-auto">
                                <!-- Tech Niche -->
                                <div class="glass-dark rounded-xl p-4 border border-lp-blue/20">
                                    <div class="text-2xl mb-2">💻</div>
                                    <h4 class="font-bold text-lp-light mb-2">TECH</h4>
                                    <ul class="text-xs text-lp-light/70 space-y-1">
                                        <li>• Scripts para devs</li>
                                        <li>• Hooks sobre IA/Crypto</li>
                                        <li>• Cases de startups</li>
                                    </ul>
                                </div>

                                <!-- Finance Niche -->
                                <div class="glass-dark rounded-xl p-4 border border-lp-green/20">
                                    <div class="text-2xl mb-2">💰</div>
                                    <h4 class="font-bold text-lp-light mb-2">FINANÇAS</h4>
                                    <ul class="text-xs text-lp-light/70 space-y-1">
                                        <li>• Scripts de investimento</li>
                                        <li>• Templates de renda passiva</li>
                                        <li>• Gatilhos de urgência</li>
                                    </ul>
                                </div>

                                <!-- Business Niche -->
                                <div class="glass-dark rounded-xl p-4 border border-lp-orange/20">
                                    <div class="text-2xl mb-2">🚀</div>
                                    <h4 class="font-bold text-lp-light mb-2">BUSINESS</h4>
                                    <ul class="text-xs text-lp-light/70 space-y-1">
                                        <li>• Cases de empreendedores</li>
                                        <li>• Scripts de escalabilidade</li>
                                        <li>• Hooks de produtividade</li>
                                    </ul>
                                </div>

                                <!-- AI Niche -->
                                <div class="glass-dark rounded-xl p-4 border border-lp-purple/20">
                                    <div class="text-2xl mb-2">🤖</div>
                                    <h4 class="font-bold text-lp-light mb-2">IA</h4>
                                    <ul class="text-xs text-lp-light/70 space-y-1">
                                        <li>• Scripts sobre automação</li>
                                        <li>• Cases de IA financeira</li>
                                        <li>• Hooks de futuro</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Email Form -->
                            <div class="glass-dark rounded-xl p-6 mb-8 max-w-2xl mx-auto">
                                <h3 class="font-bold text-lp-light mb-4">📧 Kit Completo Multi-Nicho (Tech/Finanças/Business/IA)</h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                                    <div class="text-center">
                                        <div class="text-2xl mb-1">📝</div>
                                        <span class="text-lp-light/70">+50 Scripts Personalizados</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl mb-1">🎯</div>
                                        <span class="text-lp-light/70">Templates por Nicho</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl mb-1">💰</div>
                                        <span class="text-lp-light/70">Estratégias de R$ 55K</span>
                                    </div>
                                </div>
                                <div class="space-y-4">
                                    <input type="email" id="userEmail" placeholder="Digite seu melhor email"
                                           class="w-full px-4 py-3 bg-lp-gray/50 border border-white/10 rounded-lg text-lp-light placeholder-lp-light/50 focus:outline-none focus:border-lp-blue">
                                    <button onclick="app.sendEmailDirect()" class="btn-primary text-white font-bold px-6 py-4 rounded-lg hover-lift w-full">
                                        <span id="emailButtonText">🤖 RECEBER MATERIAIS IA EMPRESARIAL</span>
                                        <div id="emailButtonLoader" class="hidden flex items-center justify-center gap-3">
                                            <div class="loading-spinner"></div>
                                            <span>Enviando materiais...</span>
                                        </div>
                                    </button>
                                    <p class="text-xs text-lp-light/50 mt-2 text-center">
                                        ⚡ Material exclusivo para influencers de TECH • FINANÇAS • BUSINESS • IA
                                    </p>
                                </div>
                                <div id="emailFeedback" class="hidden mt-4">
                                    <div class="text-lp-green text-sm">
                                        ✅ Email enviado com sucesso! Confira sua caixa de entrada.
                                    </div>
                                </div>

                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                                <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368" target="_blank" class="glass-dark rounded-xl p-6 hover-lift block">
                                    <div class="text-3xl mb-3">🏢</div>
                                    <h3 class="font-bold text-lp-light mb-2">Suporte GRIP</h3>
                                    <p class="text-sm text-lp-light/70">Suporte oficial da empresa</p>
                                </a>

                                <a href="https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368" target="_blank" class="glass-dark rounded-xl p-6 hover-lift block">
                                    <div class="text-3xl mb-3">📊</div>
                                    <h3 class="font-bold text-lp-light mb-2">Acessar Plataforma</h3>
                                    <p class="text-sm text-lp-light/70">Gerenciar seus investimentos</p>
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            }

            async handleAppDownload(platform) {
                // Pop-up estratégico antes do download
                const confirmed = await this.showStrategicPopup({
                    icon: '📱',
                    title: 'Download App GRIP',
                    message: 'Você será redirecionado para baixar o app oficial GRIP. Como influencer, você precisa conhecer a plataforma que vai indicar. IMPORTANTE: Instale o app e VOLTE AQUI para receber seu kit exclusivo!',
                    confirmText: '📲 Baixar App GRIP',
                    cancelText: '⬅️ Voltar'
                });

                if (!confirmed) return;

                const feedback = document.getElementById('downloadFeedback');
                const message = document.getElementById('downloadMessage');

                feedback.classList.remove('hidden');
                message.textContent = '📱 Redirecionando para a loja...';

                await this.sleep(1500);

                const links = {
                    android: 'https://play.google.com/store/apps/details?id=com.gaiodataos.grip&pcampaignid=web_share',
                    ios: 'https://apps.apple.com/us/app/grip-gaiodataos/id6743857628'
                };

                window.open(links[platform], '_blank');
                message.textContent = '✅ Download iniciado! Instale o app e clique no botão abaixo para continuar.';

                // Mostra botão de confirmação após 2 segundos
                setTimeout(() => {
                    document.getElementById('confirmDownloadSection').classList.remove('hidden');
                }, 2000);
            }

            confirmAppInstalled() {
                this.completeStep(2);
                this.goToStep(3);
            }

            async handleVerification() {
                // Pop-up estratégico antes da verificação
                const confirmed = await this.showStrategicPopup({
                    icon: '🎯',
                    title: 'KIT INFLUENCER PRONTO!',
                    message: 'Agora você receberá o kit exclusivo para influencers digitais indicarem IA empresarial. Renda recorrente de R$ 15.000+/mês, com potencial até R$ 55.000 Enterprise. Sistema escalável: mais empresas pagando = maior renda!',
                    confirmText: '🎯 Receber Kit Influencer',
                    cancelText: '❌ Não Quero'
                });

                if (!confirmed) return;

                const feedback = document.getElementById('verificationFeedback');
                const message = document.getElementById('verificationMessage');

                feedback.classList.remove('hidden');
                message.textContent = '🔄 Preparando seu kit exclusivo...';

                await this.sleep(2000);

                message.textContent = '✅ Tudo pronto! Preencha seu email para receber o material.';
                this.isVerified = true;

                setTimeout(() => {
                    this.completeStep(3);
                    this.goToStep(4);
                }, 2000);
            }

            copyAffiliateLink() {
                navigator.clipboard.writeText(this.affiliateLink).then(() => {
                    alert('✅ Link copiado para a área de transferência!');
                });
            }

            async sendEmailDirect() {
                const emailInput = document.getElementById('userEmail');
                const email = emailInput.value.trim();

                if (!email) {
                    alert('⚠️ Por favor, digite seu email para receber o material.');
                    emailInput.focus();
                    return;
                }

                if (!this.isValidEmail(email)) {
                    alert('⚠️ Por favor, digite um email válido.');
                    emailInput.focus();
                    return;
                }

                // Create email content
                const subject = encodeURIComponent('🤖 Materiais GRIP - Renda Recorrente Indicando IA para Empresas');
                const body = encodeURIComponent(this.createEmailMessage(email));

                // Open default email client
                window.location.href = `mailto:${email}?subject=${subject}&body=${body}`;

                // Show feedback
                document.getElementById('emailFeedback').classList.remove('hidden');

                // Also create downloadable backup
                this.downloadMaterial();
            }

            downloadMaterial() {
                const emailInput = document.getElementById('userEmail');
                const email = emailInput.value.trim() || 'usuario';

                const content = this.createEmailMessage(email);
                this.downloadEmailContent(content, email);
            }

            async sendEmail() {
                const emailInput = document.getElementById('userEmail');
                const emailButtonText = document.getElementById('emailButtonText');
                const emailButtonLoader = document.getElementById('emailButtonLoader');
                const emailFeedback = document.getElementById('emailFeedback');

                const email = emailInput.value.trim();

                if (!email) {
                    alert('⚠️ Por favor, digite seu email para receber o material.');
                    emailInput.focus();
                    return;
                }

                if (!this.isValidEmail(email)) {
                    alert('⚠️ Por favor, digite um email válido.');
                    emailInput.focus();
                    return;
                }

                // Show loading state
                emailButtonText.classList.add('hidden');
                emailButtonLoader.classList.remove('hidden');

                try {
                    // Use a real email service - Formspree (free service)
                    const emailData = {
                        email: email,
                        subject: '🤖 Materiais GRIP - Renda Recorrente Indicando IA para Empresas',
                        message: this.createEmailMessage(email),
                        affiliate_link: this.affiliateLink,
                        completion_date: new Date().toLocaleDateString('pt-BR'),
                        completion_time: new Date().toLocaleTimeString('pt-BR')
                    };

                    // Send email using Formspree
                    const response = await fetch('https://formspree.io/f/xpzgkqvw', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(emailData)
                    });

                    if (response.ok) {
                        // Show success feedback
                        emailButtonText.classList.remove('hidden');
                        emailButtonLoader.classList.add('hidden');
                        emailFeedback.classList.remove('hidden');

                        // Also create downloadable backup
                        this.createEmailContent(email);
                    } else {
                        throw new Error('Falha ao enviar email');
                    }

                } catch (error) {
                    console.error('Erro ao enviar email:', error);
                    emailButtonText.classList.remove('hidden');
                    emailButtonLoader.classList.add('hidden');
                    alert('❌ Erro ao enviar email. Tente novamente ou entre em contato conosco.');
                }
            }

            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            createEmailMessage(email) {
                return `🤖 MATERIAIS GRIP - RENDA RECORRENTE INDICANDO IA PARA EMPRESAS

Olá ${email.split('@')[0]},

🎉 PARABÉNS! Você foi aprovado para indicar IA para empresas!

💰 COMO FUNCIONA O SISTEMA:
✅ Indique soluções de IA para empresas
✅ Renda recorrente base: Superior a R$ 15.000/mês
✅ Potencial máximo: Até R$ 55.000/mês (quando empresa contrata plano Enterprise)
✅ Quanto mais empresas indicar, maior sua renda mensal
✅ Pagamentos mensais recorrentes

🔗 SEU LINK DE AFILIADO GRIP:
${this.affiliateLink}

📚 MATERIAIS EXCLUSIVOS INCLUSOS:

💻 PARA INFLUENCERS TECH:
• 15 scripts sobre IA e automação financeira
• Templates para posts sobre crypto e investimentos tech
• Cases de startups que usam GRIP
• Hooks sobre o futuro das finanças digitais

💰 PARA INFLUENCERS DE FINANÇAS:
• 20 scripts de renda passiva e investimentos
• Templates de stories sobre diversificação
• Gatilhos de urgência para produtos financeiros
• Cases de sucesso de investidores GRIP

🚀 PARA INFLUENCERS DE BUSINESS:
• 18 scripts sobre escalabilidade e ROI
• Templates para empreendedores
• Cases de empresários que investem via GRIP
• Hooks sobre produtividade financeira

🤖 PARA INFLUENCERS DE IA:
• 12 scripts sobre automação de investimentos
• Templates sobre IA financeira
• Cases de como a IA otimiza investimentos
• Hooks sobre o futuro dos investimentos automatizados

💡 ESTRATÉGIAS DE R$ 55.000/MÊS:
1. Poste 3x por semana sobre casos de sucesso
2. Use stories diários com gatilhos de urgência
3. Faça lives mensais sobre investimentos tech
4. Crie conteúdo educativo sobre IA financeira

📱 SUPORTE 24/7:
WhatsApp: https://wa.me/5511999999999
Email: <EMAIL>

🚀 PRÓXIMOS PASSOS:
- Acesse o sistema usando seu link
- Configure seu perfil de afiliado
- Comece a compartilhar e ganhar comissões

Data de conclusão: ${new Date().toLocaleDateString('pt-BR')}
Horário: ${new Date().toLocaleTimeString('pt-BR')}

Sucesso e bons ganhos! 💪

Equipe AffiliateFlow Pro`;
            }

            createEmailContent(email) {
                // Create a comprehensive email with all the information
                const emailContent = `
                    🎉 PARABÉNS! Seu Link de Afiliado AffiliateFlow Pro está Pronto!

                    Olá ${email.split('@')[0]},

                    Você completou com sucesso todas as 4 etapas do nosso sistema!

                    📋 RESUMO DA SUA CONFIGURAÇÃO:
                    ✅ Etapa 1: Boas-vindas - Concluída
                    ✅ Etapa 2: Download do App - Concluída
                    ✅ Etapa 3: Verificação - Concluída
                    ✅ Etapa 4: Finalização - Concluída

                    🔗 SEU LINK DE AFILIADO:
                    ${this.affiliateLink}

                    💰 COMO COMEÇAR A GANHAR:
                    1. Compartilhe seu link nas redes sociais
                    2. Envie para amigos e familiares
                    3. Use em grupos do WhatsApp
                    4. Publique em stories do Instagram

                    📱 SUPORTE 24/7:
                    WhatsApp: https://wa.me/5511999999999
                    Email: <EMAIL>

                    🚀 PRÓXIMOS PASSOS:
                    - Acesse o sistema usando seu link
                    - Configure seu perfil de afiliado
                    - Comece a compartilhar e ganhar comissões

                    Data de conclusão: ${new Date().toLocaleDateString('pt-BR')}
                    Horário: ${new Date().toLocaleTimeString('pt-BR')}

                    Sucesso e bons ganhos! 💪

                    Equipe AffiliateFlow Pro
                `;

                // For demonstration, we'll show this in console and create a downloadable file
                console.log('Email que seria enviado:', emailContent);

                // Create a downloadable text file with the information
                this.downloadEmailContent(emailContent, email);
            }

            downloadEmailContent(content, email) {
                const blob = new Blob([content], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `AffiliateFlow-Pro-${email.split('@')[0]}-${Date.now()}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            }

            openWhatsApp() {
                const message = encodeURIComponent(`Olá! Completei as 4 etapas do AffiliateFlow Pro e recebi meu link: ${this.affiliateLink}. Gostaria de tirar algumas dúvidas sobre como começar a ganhar comissões.`);
                window.open(`https://wa.me/5511999999999?text=${message}`, '_blank');
            }

            async handleLeadMagnetDownload() {
                const emailInput = document.getElementById('leadMagnetEmail');
                const email = emailInput.value.trim();

                if (!email) {
                    alert('Por favor, insira seu email para receber o guia gratuito!');
                    return;
                }

                if (!this.isValidEmail(email)) {
                    alert('Por favor, insira um email válido!');
                    return;
                }

                const feedback = document.getElementById('leadMagnetFeedback');
                const message = document.getElementById('leadMagnetMessage');

                feedback.classList.remove('hidden');
                message.textContent = 'Enviando seu guia exclusivo...';

                try {
                    // Simular envio de email (substitua por EmailJS real)
                    await this.sleep(2000);

                    // Criar e baixar o PDF como backup
                    this.downloadLeadMagnetPDF(email);

                    message.textContent = '✅ Guia enviado! Verifique seu email e continue para o próximo passo.';

                    // Armazenar lead
                    this.storeLead(email);

                    // Avançar para próxima etapa após 3 segundos
                    setTimeout(() => {
                        this.completeStepAndAdvance(0, 1);
                    }, 3000);

                } catch (error) {
                    console.error('Erro ao enviar lead magnet:', error);
                    message.textContent = '❌ Erro ao enviar. Baixando arquivo...';
                    this.downloadLeadMagnetPDF(email);
                }
            }

            downloadLeadMagnetPDF(email) {
                // Simular download do PDF
                const pdfUrl = './pdf/Domine-a-Nova-Economia-Digital.pdf';
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.download = 'Domine-a-Nova-Economia-Digital.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('📚 PDF baixado para:', email);
            }

            storeLead(email) {
                const leads = JSON.parse(localStorage.getItem('leads') || '[]');
                const leadData = {
                    email: email,
                    timestamp: Date.now(),
                    source: 'lead_magnet',
                    status: 'active'
                };

                // Evitar duplicatas
                if (!leads.find(lead => lead.email === email)) {
                    leads.push(leadData);
                    localStorage.setItem('leads', JSON.stringify(leads));
                    console.log('💾 Lead armazenado:', leadData);
                }
            }

            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Initialize the application
        const app = new AffiliateFlowApp();
    </script>
</body>
</html>
