// Advanced Email Marketing Automation System
import emailjs from '@emailjs/browser';

// Enhanced Email Templates for Marketing Sequences with Niche Targeting
export const EMAIL_TEMPLATES = {
  WELCOME: {
    subject: '🚀 Bem-vindo ao GRIP - Renda Recorrente R$ 15K+ Indicando IA!',
    template: 'welcome_template'
  },
  LEAD_MAGNET: {
    subject: '📚 Seu Guia "Domine a Nova Economia Digital" está aqui!',
    template: 'lead_magnet_delivery'
  },
  ONBOARDING_DAY1: {
    subject: '💰 Como Ganhar R$ 15.000/mês Indicando IA para Empresas',
    template: 'onboarding_day1'
  },
  ONBOARDING_DAY3: {
    subject: '🎯 3 Estratégias que Geram R$ 55K (Plano Enterprise)',
    template: 'onboarding_day3'
  },
  ONBOARDING_DAY7: {
    subject: '⚡ URGENTE: Kit Influencer Multi-Nicho (Tech/Finanças/Business/IA)',
    template: 'onboarding_day7'
  },
  NICHE_TECH: {
    subject: '💻 Scripts Exclusivos para Tech Influencers - GRIP IA',
    template: 'niche_tech'
  },
  NICHE_FINANCE: {
    subject: '💰 Templates de Renda Passiva com IA Financeira',
    template: 'niche_finance'
  },
  NICHE_BUSINESS: {
    subject: '🚀 Cases de Empreendedores que Faturam R$ 55K com IA',
    template: 'niche_business'
  },
  NICHE_AI: {
    subject: '🤖 Hooks de IA que Convertem: Automação Empresarial',
    template: 'niche_ai'
  },
  CONVERSION_FOLLOW_UP: {
    subject: '🔥 Seu Link GRIP Está Ativo - Primeiros R$ 15K em 30 dias',
    template: 'conversion_followup'
  },
  REACTIVATION: {
    subject: '😢 Última Chance: R$ 55K Enterprise + Kit Multi-Nicho',
    template: 'reactivation'
  },
  SOCIAL_PROOF: {
    subject: '📊 Como @RafaelTech Faturou R$ 31.500 em 60 dias',
    template: 'social_proof'
  }
};

// Niche targeting for personalized content
export enum UserNiche {
  TECH = 'tech',
  FINANCE = 'finance',
  BUSINESS = 'business',
  AI = 'ai',
  GENERAL = 'general'
}

export interface EmailSequenceData {
  name: string;
  email: string;
  leadSource: string;
  niche?: UserNiche;
  affiliateLink?: string;
  customData?: Record<string, any>;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

// Niche-specific content templates
export const NICHE_CONTENT = {
  [UserNiche.TECH]: {
    hooks: [
      "🔥 Como a IA está revolucionando o desenvolvimento de software",
      "💻 Startups que faturam milhões usando IA financeira",
      "⚡ Automação que todo dev deveria conhecer"
    ],
    caseStudies: [
      "@RafaelTech: R$ 31.500 em 60 dias falando sobre IA financeira",
      "Dev que automatizou vendas e fatura R$ 45K/mês",
      "Como criar conteúdo tech que converte"
    ],
    scripts: [
      "Script: 'A IA que está mudando o mercado financeiro'",
      "Hook: 'Todo dev precisa saber disso sobre IA'",
      "CTA: 'Descubra a IA que empresas pagam R$ 55K'"
    ]
  },
  [UserNiche.FINANCE]: {
    hooks: [
      "💰 IA que está revolucionando investimentos",
      "📈 Como grandes fundos usam IA para lucrar",
      "🎯 Renda passiva com tecnologia financeira"
    ],
    caseStudies: [
      "Investidor que diversificou com IA: R$ 55.800 em 90 dias",
      "Como ensinar ROI com IA e faturar R$ 25K/mês",
      "Estratégia de renda recorrente para finfluencers"
    ],
    scripts: [
      "Script: 'A IA que está mudando investimentos'",
      "Hook: 'Renda passiva que todo investidor deveria conhecer'",
      "CTA: 'Descubra como empresas pagam R$ 55K por isso'"
    ]
  },
  [UserNiche.BUSINESS]: {
    hooks: [
      "🚀 IA que está transformando empresas",
      "💼 Como PMEs competem com grandes corporações",
      "⚡ Automação que todo empreendedor precisa"
    ],
    caseStudies: [
      "@CarlaEmpreende: R$ 55.800 ensinando sobre ROI com IA",
      "Empreendedor que automatizou vendas: R$ 40K/mês",
      "Como escalar negócios com IA empresarial"
    ],
    scripts: [
      "Script: 'A IA que está revolucionando PMEs'",
      "Hook: 'Todo empreendedor deveria saber disso'",
      "CTA: 'Veja como empresas pagam R$ 55K por essa IA'"
    ]
  },
  [UserNiche.AI]: {
    hooks: [
      "🤖 IA financeira que empresas pagam R$ 55K",
      "⚡ Automação que está mudando o mercado",
      "🔥 Como a IA está criando novos milionários"
    ],
    caseStudies: [
      "Especialista em IA que fatura R$ 60K/mês",
      "Como monetizar conhecimento em IA",
      "Estratégias de IA que geram renda recorrente"
    ],
    scripts: [
      "Script: 'A IA que está criando milionários'",
      "Hook: 'O futuro da IA empresarial chegou'",
      "CTA: 'Descubra a IA que vale R$ 55K para empresas'"
    ]
  }
};

// Enhanced Email Marketing Service Class with Niche Intelligence
export class EmailMarketingService {
  private serviceId: string;
  private publicKey: string;
  private templateId: string;

  constructor() {
    this.serviceId = import.meta.env.VITE_EMAILJS_SERVICE_ID || 'service_grip_ai';
    this.publicKey = import.meta.env.VITE_EMAILJS_PUBLIC_KEY || 'grip_public_key';
    this.templateId = import.meta.env.VITE_EMAILJS_TEMPLATE_ID || 'template_grip_ai';

    if (this.publicKey) {
      emailjs.init(this.publicKey);
    }
  }

  // Auto-detect user niche based on email domain and UTM data
  private detectUserNiche(data: EmailSequenceData): UserNiche {
    const email = data.email.toLowerCase();
    const utmSource = data.utmSource?.toLowerCase() || '';
    const utmCampaign = data.utmCampaign?.toLowerCase() || '';

    // Tech indicators
    if (email.includes('dev') || email.includes('tech') ||
        utmSource.includes('tech') || utmCampaign.includes('dev')) {
      return UserNiche.TECH;
    }

    // Finance indicators
    if (email.includes('invest') || email.includes('finance') ||
        utmSource.includes('finance') || utmCampaign.includes('invest')) {
      return UserNiche.FINANCE;
    }

    // Business indicators
    if (email.includes('business') || email.includes('empreend') ||
        utmSource.includes('business') || utmCampaign.includes('empreend')) {
      return UserNiche.BUSINESS;
    }

    // AI indicators
    if (email.includes('ai') || email.includes('artificial') ||
        utmSource.includes('ai') || utmCampaign.includes('artificial')) {
      return UserNiche.AI;
    }

    return UserNiche.GENERAL;
  }

  // Get personalized content based on niche
  private getPersonalizedContent(niche: UserNiche, contentType: 'hooks' | 'caseStudies' | 'scripts'): string {
    const nicheContent = NICHE_CONTENT[niche];
    if (!nicheContent) return '';

    const content = nicheContent[contentType];
    return content[Math.floor(Math.random() * content.length)];
  }

  // Send lead magnet with personalized content
  async sendLeadMagnet(data: EmailSequenceData): Promise<boolean> {
    try {
      const detectedNiche = data.niche || this.detectUserNiche(data);
      const personalizedHook = this.getPersonalizedContent(detectedNiche, 'hooks');
      const personalizedCase = this.getPersonalizedContent(detectedNiche, 'caseStudies');

      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - GRIP IA Empresarial',
        subject: EMAIL_TEMPLATES.LEAD_MAGNET.subject,

        // Personalized content based on niche
        personalized_hook: personalizedHook,
        personalized_case: personalizedCase,
        niche: detectedNiche,

        // Lead magnet content
        lead_magnet_title: 'Domine a Nova Economia Digital',
        lead_magnet_subtitle: 'Guia Estratégico para Renda Recorrente R$ 15K+ com IA Empresarial',

        // PDF download link
        pdf_download_link: `${window.location.origin}/pdf/Domine-a-Nova-Economia-Digital.pdf`,

        // Next steps with urgency
        next_steps: this.getPersonalizedNextSteps(detectedNiche),

        // Social proof specific to niche
        social_proof: this.getNicheSpecificProof(detectedNiche),

        // CTA based on niche
        cta_text: this.getNicheCTA(detectedNiche),
        cta_link: data.affiliateLink || 'https://grip.gaiodataos.com/?si=f722bc5f-c550-4368-a50f-d727e7abc368'
      };

      await emailjs.send(this.serviceId, this.templateId, templateParams);

      // Schedule personalized follow-up sequence
      this.schedulePersonalizedSequence(data, detectedNiche);

      return true;
    } catch (error) {
      console.error('Lead magnet email error:', error);
      return false;
    }
  }

  // Send welcome email with immediate value
  async sendWelcomeEmail(data: EmailSequenceData): Promise<boolean> {
    try {
      const detectedNiche = data.niche || this.detectUserNiche(data);

      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - GRIP IA Empresarial',
        subject: EMAIL_TEMPLATES.WELCOME.subject,

        // Personalized content
        lead_source: data.leadSource,
        affiliate_link: data.affiliateLink || '',
        niche: detectedNiche,

        // Welcome message with immediate value
        welcome_message: this.getPersonalizedWelcomeMessage(data.name, detectedNiche),

        // Niche-specific bonus content
        bonus_content: this.getNicheBonusContent(detectedNiche),

        // Next steps
        next_steps: this.getPersonalizedNextSteps(detectedNiche),

        // Social proof
        social_proof: this.getNicheSpecificProof(detectedNiche)
      };

      await emailjs.send(this.serviceId, this.templateId, templateParams);

      // Schedule follow-up emails
      this.schedulePersonalizedSequence(data, detectedNiche);

      return true;
    } catch (error) {
      console.error('Welcome email error:', error);
      return false;
    }
  }

  // Schedule automated email sequence
  private scheduleEmailSequence(data: EmailSequenceData): void {
    // Day 1: Onboarding and first strategy
    setTimeout(() => {
      this.sendOnboardingDay1(data);
    }, 24 * 60 * 60 * 1000); // 24 hours

    // Day 3: Advanced strategies
    setTimeout(() => {
      this.sendOnboardingDay3(data);
    }, 3 * 24 * 60 * 60 * 1000); // 3 days

    // Day 7: Final push with urgency
    setTimeout(() => {
      this.sendOnboardingDay7(data);
    }, 7 * 24 * 60 * 60 * 1000); // 7 days
  }

  // Day 1 onboarding email
  private async sendOnboardingDay1(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY1.subject,
        
        main_content: `
          Olá ${data.name}!
          
          Espero que esteja animado(a) para começar sua jornada rumo aos R$ 15.000/mês!
          
          Hoje vou compartilhar com você a PRIMEIRA estratégia que mudou a vida de mais de 2.847 afiliados:
          
          🎯 **ESTRATÉGIA #1: O Método da Indicação Inteligente**
          
          Ao invés de "vender" diretamente, você vai EDUCAR seu público sobre:
          ✅ Como a IA está revolucionando empresas
          ✅ Por que grandes corporações estão investindo bilhões
          ✅ Como pequenas empresas podem competir usando as mesmas ferramentas
          
          **AÇÃO PARA HOJE:**
          1. Acesse seu painel de afiliado: ${data.affiliateLink || '[LINK_SERÁ_ENVIADO]'}
          2. Baixe o kit de materiais exclusivos
          3. Compartilhe o primeiro conteúdo educativo
          
          **RESULTADO ESPERADO:** Suas primeiras comissões em 48-72h
          
          Amanhã vou revelar a Estratégia #2 que pode TRIPLICAR seus resultados...
          
          Forte abraço,
          Marcus Silva
          
          P.S.: Mais de 847 pessoas já estão aplicando essa estratégia HOJE. Não fique para trás!
        `,
        
        cta_text: 'ACESSAR PAINEL DE AFILIADO',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY1.template, templateParams);
    } catch (error) {
      console.error('Day 1 email error:', error);
    }
  }

  // Day 3 advanced strategies
  private async sendOnboardingDay3(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY3.subject,
        
        main_content: `
          ${data.name}, como estão os primeiros resultados?
          
          Espero que já tenha visto suas primeiras comissões chegando! 💰
          
          Hoje vou revelar as 3 ESTRATÉGIAS SECRETAS que os top afiliados (R$ 50k/mês) usam:
          
          🔥 **ESTRATÉGIA SECRETA #1: O Funil da Autoridade**
          - Como se posicionar como especialista em IA
          - Scripts prontos para redes sociais
          - Técnica do "Problema → Solução → Prova"
          
          ⚡ **ESTRATÉGIA SECRETA #2: O Método da Escassez Inteligente**
          - Como criar urgência sem ser "vendedor"
          - Gatilhos psicológicos que convertem 3x mais
          - Timing perfeito para máxima conversão
          
          🎯 **ESTRATÉGIA SECRETA #3: A Rede de Indicações Exponencial**
          - Como transformar 1 cliente em 10 indicações
          - Sistema de recompensas que funciona
          - Automação completa do processo
          
          **BÔNUS EXCLUSIVO:** Planilha de controle de comissões + Calculadora de metas
          
          Acesse agora: ${data.affiliateLink || '[LINK_PAINEL]'}
          
          Nos vemos no topo!
          Marcus Silva
        `,
        
        bonus_content: 'Planilha Exclusiva + Scripts Prontos',
        cta_text: 'BAIXAR ESTRATÉGIAS SECRETAS',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY3.template, templateParams);
    } catch (error) {
      console.error('Day 3 email error:', error);
    }
  }

  // Day 7 final push with urgency
  private async sendOnboardingDay7(data: EmailSequenceData): Promise<void> {
    try {
      const templateParams = {
        to_name: data.name,
        to_email: data.email,
        from_name: 'Marcus Silva - AffiliateFlow',
        subject: EMAIL_TEMPLATES.ONBOARDING_DAY7.subject,
        
        main_content: `
          ${data.name}, esta é sua ÚLTIMA CHANCE...
          
          Nos últimos 7 dias, você recebeu estratégias que valem mais de R$ 5.000.
          
          Mas hoje quero fazer uma oferta ESPECIAL só para você:
          
          🚨 **ACESSO VIP AO MÉTODO DOS R$ 15K/MÊS** 🚨
          
          ✅ Mentoria individual comigo (1h)
          ✅ Grupo VIP no Telegram (apenas 100 vagas)
          ✅ Scripts de conversão que faturam R$ 50k/mês
          ✅ Suporte prioritário 24/7
          ✅ Comissões DOBRADAS nos primeiros 30 dias
          
          **VALOR NORMAL:** R$ 2.997
          **SEU PREÇO HOJE:** R$ 497 (83% OFF)
          
          ⏰ **OFERTA EXPIRA EM 24 HORAS**
          
          Apenas 23 vagas restantes...
          
          Esta é SUA chance de sair do comum e entrar para o grupo dos R$ 15k/mês.
          
          Não deixe para amanhã o que pode mudar sua vida HOJE!
          
          [GARANTIR MINHA VAGA VIP]
          
          Último aviso,
          Marcus Silva
          
          P.S.: Se não aproveitar hoje, essa oferta não voltará. Decida AGORA!
        `,
        
        urgency_timer: '24:00:00',
        special_price: 'R$ 497',
        normal_price: 'R$ 2.997',
        discount: '83% OFF',
        cta_text: 'GARANTIR VAGA VIP AGORA',
        cta_link: data.affiliateLink || '#'
      };

      await emailjs.send(this.serviceId, EMAIL_TEMPLATES.ONBOARDING_DAY7.template, templateParams);
    } catch (error) {
      console.error('Day 7 email error:', error);
    }
  }

  // Helper methods for content generation
  private getWelcomeMessage(name: string): string {
    return `
      Olá ${name}!
      
      Seja muito bem-vindo(a) ao AffiliateFlow Premium! 🎉
      
      Você acabou de dar o primeiro passo rumo à sua independência financeira.
      
      Nos próximos minutos, você vai descobrir exatamente como transformar
      indicações simples em uma renda recorrente de R$ 15.000/mês.
    `;
  }

  private getBonusContent(): string {
    return `
      🎁 **BÔNUS EXCLUSIVOS PARA VOCÊ:**
      
      ✅ E-book: "7 Segredos dos Afiliados Milionários"
      ✅ Planilha de Controle de Comissões
      ✅ Scripts Prontos para Redes Sociais
      ✅ Vídeo: "Primeira Venda em 24h"
      ✅ Acesso ao Grupo VIP no Telegram
    `;
  }

  private getNextSteps(): string {
    return `
      📋 **SEUS PRÓXIMOS PASSOS:**
      
      1️⃣ Confirme seu email (se ainda não fez)
      2️⃣ Acesse seu painel de afiliado
      3️⃣ Baixe os materiais exclusivos
      4️⃣ Faça sua primeira indicação
      5️⃣ Acompanhe suas comissões em tempo real
    `;
  }

  private getSocialProof(): string {
    return `
      🏆 **RESULTADOS REAIS DOS NOSSOS AFILIADOS:**
      
      "Em 30 dias faturei R$ 12.847 só com indicações!" - Ana Paula, SP
      "Método simples que realmente funciona. R$ 8.500 no primeiro mês!" - Carlos, RJ  
      "Nunca pensei que fosse tão fácil. R$ 15.200 em 45 dias!" - Mariana, MG
    `;
  }
}

// Initialize email marketing service
export const emailMarketing = new EmailMarketingService();
